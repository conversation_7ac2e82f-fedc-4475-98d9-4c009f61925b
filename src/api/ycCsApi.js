import CsConstant from "@/api/CsConstant";
import {updatePlanWithDetails} from "@/api/importedCigarettes/plan/planApi";
import {
  addPushListInvoice,
  batchUpdateBoxList,
  checkNextModuleExistEffectiveData,
  deletePurchaseListBox,
  enableQuoList,
  generateIOrder,
  getDeliveryOrderCertByHeadSid,
  getDeliveryOrderShipmentsByHeadSid,
  getOrderListBySid,
  getPurchaseListBySid,
  innerDecTotalUpdatePurchaseList, listInDeliveryOrderHead
} from "@/api/cs_api_constant";
import {checkTransferNotice} from "@/api/auxiliaryMaterials/buyContract/buyContractApi";
import {insertByContract} from "@/api/payment/payment_info";
const baseUri = CsConstant.PREFIX_CS
export default {

  aeoManage: {
    aeoReview: {
      selectListBySid: CsConstant.PREFIX_CS + `/v1/aeoAuditInfo/selectListBySid`
    },
    fieldMarking: {
      getBySidAndFormType: CsConstant.PREFIX_CS + `/v1/bizFieldMarking/getBySidAndFormType`,
      insert: CsConstant.PREFIX_CS + `/v1/bizFieldMarking/insert`,
      update: CsConstant.PREFIX_CS + `/v1/bizFieldMarking/update`,
      saveOrUpdate: CsConstant.PREFIX_CS + `/v1/bizFieldMarking/saveOrUpdate`,
    }
  },

  /* 上传附件请求 */
  baseAttach: {
    insert: CsConstant.PREFIX_CS + 'v1/attached/insert',
    getAttechedList: CsConstant.PREFIX_CS + 'v1/attached/getAttechedList',
    getAttechedListByType: CsConstant.PREFIX_CS + 'v1/attached/getAttechedListByType',
    download: CsConstant.PREFIX_CS + 'v1/attached',
    delete: CsConstant.PREFIX_CS + 'v1/attached',
    // 根据sid获取文件信息
    getAttachFile: CsConstant.PREFIX_CS + 'v1/attached/getAttachFile',
  },


  /* 海关参数自定义信息url */
  baseInfoCustomerParams: {
    list: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams/list',
    update: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams',
    insert: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams',
    delete: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams',
  },


  /* 客户基础信息url */
  biClientInfo: {
    list: CsConstant.PREFIX_CS + 'v1/biClientInformation/list',
    update: CsConstant.PREFIX_CS + 'v1/biClientInformation',
    insert: CsConstant.PREFIX_CS + 'v1/biClientInformation',
    delete: CsConstant.PREFIX_CS + 'v1/biClientInformation',
    export: CsConstant.PREFIX_CS + 'v1/biClientInformation/export',
  },

  /* shipfrom 基础信息url */
  biShipfrom: {
    list: CsConstant.PREFIX_CS + 'v1/biShipfrom/list',
    update: CsConstant.PREFIX_CS + 'v1/biShipfrom',
    insert: CsConstant.PREFIX_CS + 'v1/biShipfrom',
    delete: CsConstant.PREFIX_CS + 'v1/biShipfrom',
    export: CsConstant.PREFIX_CS + 'v1/biShipfrom/export',
  },

  /**
   * 审批流接口
   */
  approvalFlow: {
    sendAudit: CsConstant.PREFIX_CS + 'v1/approvalFlow/sendAudit',
    audit: CsConstant.PREFIX_CS + 'v1/approvalFlow/audit',
    reject: CsConstant.PREFIX_CS + 'v1/approvalFlow/reject',
  },

  /*
  进口卷烟
   */
  importedCigarettes: {
    contract: {
      list: CsConstant.PREFIX_CS + 'v1/bizIContractHead/list',
      aeoList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/aeoList',
      planList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/planList',
      customsList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/customsList',
      update: CsConstant.PREFIX_CS + 'v1/bizIContractHead',
      insert: CsConstant.PREFIX_CS + 'v1/bizIContractHead',
      delete: CsConstant.PREFIX_CS + 'v1/bizIContractHead',
      export: CsConstant.PREFIX_CS + 'v1/bizIContractHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizIContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizIContractHead/sendAudit',
      audit: CsConstant.PREFIX_CS + 'v1/bizIContractHead/audit',
      reject: CsConstant.PREFIX_CS + 'v1/bizIContractHead/reject',
      cancel: CsConstant.PREFIX_CS + 'v1/bizIContractHead/cancel',
      checkStatus: CsConstant.PREFIX_CS + 'v1/bizIContractHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/bizIContractHead/copy',
      sellerList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/sellerList',
    },
    contractList: {
      list: CsConstant.PREFIX_CS + 'v1/bizIContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizIContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizIContractList',
      delete: CsConstant.PREFIX_CS + 'v1/bizIContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizIContractList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/bizIContractList/getContractTotal',
    },
    plan : {
      list: CsConstant.PREFIX_CS + '/v1/bizIplan/list',
      insert: CsConstant.PREFIX_CS + '/v1/bizIplan',
      delete: CsConstant.PREFIX_CS + '/v1/bizIplan',
      update: CsConstant.PREFIX_CS + '/v1/bizIplan',
      export: CsConstant.PREFIX_CS + '/v1/bizIplan/export',
      confirm: CsConstant.PREFIX_CS + '/v1/bizIplan/confirm',
      sendAudit: CsConstant.PREFIX_CS + '/v1/bizIplan/sendApproval',
      invalidate: CsConstant.PREFIX_CS + '/v1/bizIplan/invalidate',
      checkPlanIdNotCancel: CsConstant.PREFIX_CS + '/v1/bizIplan/checkPlanIdNotCancel',
      copyVersion: CsConstant.PREFIX_CS + '/v1/bizIplan/copyVersion',
      insertPlanWithDetails: CsConstant.PREFIX_CS + '/v1/bizIplan/insertPlanWithDetails',
      updatePlanWithDetails: CsConstant.PREFIX_CS + '/v1/bizIplan/updatePlanWithDetails',
      validateDetailData: CsConstant.PREFIX_CS + '/v1/bizIplan/validateDetailData'
    },
    planList : {
      list: CsConstant.PREFIX_CS + '/v1/bizIPlanList/list',
      insert: CsConstant.PREFIX_CS + '/v1/bizIPlanList',
      delete: CsConstant.PREFIX_CS + '/v1/bizIPlanList',
      update: CsConstant.PREFIX_CS + '/v1/bizIPlanList',
      export:CsConstant.PREFIX_CS + '/v1/bizIPlanList/export'
    }


  },
  /**
   * 进口辅料
   */
  auxiliaryMaterials:{
    bizIAuxMatForContractHead : {
      list: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/list',
      planList: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/planList',
      sellerList: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/sellerList',
      buyerList: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/buyerList',
      customsList: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/customsList',
      update: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead',
      insert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead',
      delete: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead',
      export: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/sendAudit',
      cancel: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/cancel',
      checkStatus: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/copy',
    },
    bizIAuxMatForContractList : {
      list: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList',
      batchInsert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/batchInsert',
      delete: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/getContractTotal',
    },
    buyContract: {
      list: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/list',
      update: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract',
      insert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract',
      delete: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract',
      export: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/sendApproval',
      invalidate: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/invalidate',
      checkNotCancel: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/checkContractIdNotCancel',
      copyVersion: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/copyVersion',
      insertBuyContractWithDetails: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/insertBuyContractWithDetails',
      updateBuyContractWithDetails: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/updateBuyContractWithDetails',
      validateDetailData: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/validateDetailData',
      handlerTransferNotice: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/handlerTransferNotice',
      saveTransferNotice: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/saveTransferNotice',
      checkTransferNotice: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/checkTransferNotice',
      saveTransferNoticePrint: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContract/saveTransferNoticePrint',
    },
    buyContractList: {
      list: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContractList',
      delete: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizIAuxmatBuyContractList/export'
    },
    contract: {
      list: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/list',
      planList: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/planList',
      sellerList: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/sellerList',
      update: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead',
      insert: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead',
      delete: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead',
      export: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/sendAudit',
      cancel: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/cancel',
      checkStatus: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/copy',
    },
    contractList: {
      list: CsConstant.PREFIX_CS + 'v1/bizForeignContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizForeignContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizForeignContractList',
      delete: CsConstant.PREFIX_CS + 'v1/bizForeignContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizForeignContractList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/bizForeignContractList/getContractTotal',
    },
    // 订货通知
    orderNotice: {
      head: {
        list: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/list',
        getPortOptions: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/portOptions',
        getAllCustomers: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/customers',
        insert: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead',
        update: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead',
        delete: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead',
        export: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/export',
        getSelectData: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/getSelectContractInfo',
        getAddData: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/getAddRequiredData',
        confirm: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/confirm',
        invalidate: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/invalidate',
        checkVersionCopy: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/checkVersionCopy',
        versionCopy: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/versionCopy',
        printOrderForm: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/printOrderForm'
      },
      body: {
        list: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeList/list',
        getSummary: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeList/summary',
        update: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeList',
        delete: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeList',
        getAddListDetailData: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/getAddDetailData',
        addListDetail: CsConstant.PREFIX_CS + 'v1/importAuxMat/orderNoticeHead/addDetail'
      }
    }
  },
  /*进口费用*/
  costI:{
    list: CsConstant.PREFIX_CS + 'v1/expenseIHead/list',
    update: CsConstant.PREFIX_CS + 'v1/expenseIHead',
    insert: CsConstant.PREFIX_CS + 'v1/expenseIHead',
    delete: CsConstant.PREFIX_CS + 'v1/expenseIHead',
    chargeback: CsConstant.PREFIX_CS + 'v1/expenseIHead/chargeback',
    affirm: CsConstant.PREFIX_CS + 'v1/expenseIHead/affirm',
    cancellation: CsConstant.PREFIX_CS + 'v1/expenseIHead/cancellation',
    copy: CsConstant.PREFIX_CS + 'v1/expenseIHead/copy',
    export: CsConstant.PREFIX_CS + 'v1/expenseIHead/export',
    Ilist:{
      list: CsConstant.PREFIX_CS + 'v1/expenseIList/list',
      update: CsConstant.PREFIX_CS + 'v1/expenseIList',
      delete: CsConstant.PREFIX_CS + 'v1/expenseIList',
      export: CsConstant.PREFIX_CS + 'v1/expenseIList/export',
      getSumDataCost: CsConstant.PREFIX_CS + 'v1/expenseIList/getSumDataByInvoiceSummary',
      IContract: {
        selectCostType: CsConstant.PREFIX_CS + 'v1/expenseIList/selectCostType',
       list: CsConstant.PREFIX_CS + 'v1/expenseIList/listContract',
       listPayment: CsConstant.PREFIX_CS + 'v1/expenseIList/listContractPayment',
        listPayment3: CsConstant.PREFIX_CS + 'v1/expenseIList/listContractPayment3',
        listAuxmatContractPayment: CsConstant.PREFIX_CS + 'v1/expenseIList/listAuxmatContractPayment',
        listAuxnonContractPayment: CsConstant.PREFIX_CS + 'v1/expenseIList/listAuxnonContractPayment',
        listAuxnonContractPaymentCost: CsConstant.PREFIX_CS + 'v1/expenseIList/listAuxnonContractPaymentCost',
       insert: CsConstant.PREFIX_CS + 'v1/expenseIList/insertContractDetail',
       insert6: CsConstant.PREFIX_CS + 'v1/expenseIList/insertContractDetail6',
      },
      shippingOrder: {
        list: CsConstant.PREFIX_CS + 'v1/expenseIList/listShippingOrder',
        list6: CsConstant.PREFIX_CS + 'v1/expenseIList/listShippingOrder6',
        listPayment: CsConstant.PREFIX_CS + 'v1/expenseIList/listShippingOrderPayment',
        listPayment2: CsConstant.PREFIX_CS + 'v1/expenseIList/listPayment2',
        listPayment6: CsConstant.PREFIX_CS + 'v1/expenseIList/listPayment6',
        listPayment3: CsConstant.PREFIX_CS + 'v1/expenseIList/listPayment3',
        insert: CsConstant.PREFIX_CS + 'v1/expenseIList/insertShippingOrder',
        insert6: CsConstant.PREFIX_CS + 'v1/expenseIList/insertShippingOrder6',
        upload: CsConstant.PREFIX_CS + 'v1/expenseIList/upload',
      }
    }
  },
  /* 客商信息 基础信息url */
    bizMerchant : {
      list: CsConstant.PREFIX_CS + `/v1/bizMerchant/list`,
      insert: CsConstant.PREFIX_CS + `/v1/bizMerchant`,
      delete: CsConstant.PREFIX_CS + `/v1/bizMerchant`,
      update: CsConstant.PREFIX_CS + `/v1/bizMerchant`,
      export: CsConstant.PREFIX_CS + `/v1/bizMerchant/export`,
      getMerchantCode: CsConstant.PREFIX_CS + `/v1/bizMerchant/getMerchantCode`,
      getDefaultBuyer: CsConstant.PREFIX_CS + `/v1/bizMerchant/getDefaultBuyer`,
      getForAggrSearch: CsConstant.PREFIX_CS + `/v1/bizMerchant/getForAggrSearch`,
    },
  bizMaterialInformation : {
    list: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/list`,
    insert: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation`,
    delete: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation`,
    update: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation`,
    getMerchantCodeValue: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/getMerchantCodeValue`,
    export: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/export`,
    cancel: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/cancel`,
    matForPlan: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/matForPlan`,
  },
  /* 企业参数 */
  params:{
    storehouse : {
      list: CsConstant.PREFIX_CS + `/v1/storehouse/list`,
      insert: CsConstant.PREFIX_CS + `/v1/storehouse`,
      delete: CsConstant.PREFIX_CS + `/v1/storehouse`,
      update: CsConstant.PREFIX_CS + `/v1/storehouse`,
      export: CsConstant.PREFIX_CS + `/v1/storehouse/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/storehouse/getNextCode`,
    },
    productType : {
      list: CsConstant.PREFIX_CS + `/v1/productType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/productType`,
      delete: CsConstant.PREFIX_CS + `/v1/productType`,
      update: CsConstant.PREFIX_CS + `/v1/productType`,
      export: CsConstant.PREFIX_CS + `/v1/productType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/productType/getNextCode`,
    },
    packageInfo : {
      list: CsConstant.PREFIX_CS + `/v1/packageInfo/list`,
      listAll: CsConstant.PREFIX_CS + `/v1/packageInfo/listAll`,
      insert: CsConstant.PREFIX_CS + `/v1/packageInfo`,
      delete: CsConstant.PREFIX_CS + `/v1/packageInfo`,
      update: CsConstant.PREFIX_CS + `/v1/packageInfo`,
      export: CsConstant.PREFIX_CS + `/v1/packageInfo/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/packageInfo/getNextCode`,
    },
    costType : {
      list: CsConstant.PREFIX_CS + `/v1/costType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/costType`,
      delete: CsConstant.PREFIX_CS + `/v1/costType`,
      update: CsConstant.PREFIX_CS + `/v1/costType`,
      export: CsConstant.PREFIX_CS + `/v1/costType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/costType/getNextCode`,
    },
    rateTable : {
      list: CsConstant.PREFIX_CS + `/v1/rateTable/list`,
      insert: CsConstant.PREFIX_CS + `/v1/rateTable`,
      delete: CsConstant.PREFIX_CS + `/v1/rateTable`,
      update: CsConstant.PREFIX_CS + `/v1/rateTable`,
      export: CsConstant.PREFIX_CS + `/v1/rateTable/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/rateTable/getNextCode`,
    },
    enterpriseRate : {
      list: CsConstant.PREFIX_CS + `/v1/EnterpriseRate/list`,
      insert: CsConstant.PREFIX_CS + `/v1/EnterpriseRate`,
      delete: CsConstant.PREFIX_CS + `/v1/EnterpriseRate`,
      update: CsConstant.PREFIX_CS + `/v1/EnterpriseRate`,
      export: CsConstant.PREFIX_CS + `/v1/EnterpriseRate/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/EnterpriseRate/getNextCode`,
    },
    priceTerms: {
      list: CsConstant.PREFIX_CS + '/v1/priceTerms/list',
      listAll: CsConstant.PREFIX_CS + '/v1/priceTerms/listAll',
      insert: CsConstant.PREFIX_CS + '/v1/priceTerms',
      update: CsConstant.PREFIX_CS + '/v1/priceTerms',
      delete: CsConstant.PREFIX_CS + '/v1/priceTerms',
      export: CsConstant.PREFIX_CS + '/v1/priceTerms/export',
      getSno: CsConstant.PREFIX_CS + '/v1/priceTerms/nextParamCode',
      all: CsConstant.PREFIX_CS + '/v1/priceTerms/all',
    },
    city: {
      list: CsConstant.PREFIX_CS + '/v1/city/list',
      insert: CsConstant.PREFIX_CS + '/v1/city',
      update: CsConstant.PREFIX_CS + '/v1/city',
      delete: CsConstant.PREFIX_CS + '/v1/city',
      export: CsConstant.PREFIX_CS + '/v1/city/export',
      getSno: CsConstant.PREFIX_CS + '/v1/city/nextParamCode',
      getShanghaiCode: CsConstant.PREFIX_CS + '/v1/city/getShanghaiCode',
      all: CsConstant.PREFIX_CS + '/v1/city/all',
    },
    transCode: {
      list: CsConstant.PREFIX_CS + '/v1/transCode/list',
      insert: CsConstant.PREFIX_CS + '/v1/transCode',
      update: CsConstant.PREFIX_CS + '/v1/transCode',
      delete: CsConstant.PREFIX_CS + '/v1/transCode',
      export: CsConstant.PREFIX_CS + '/v1/transCode/export',
      copy: CsConstant.PREFIX_CS + '/v1/transCode/copy',


    },

    boxType : {
      list: CsConstant.PREFIX_CS + `/v1/boxType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/boxType`,
      delete: CsConstant.PREFIX_CS + `/v1/boxType`,
      update: CsConstant.PREFIX_CS + `/v1/boxType`,
      export: CsConstant.PREFIX_CS + `/v1/boxType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/boxType/getNextCode`,
      getBoxTypeMap: CsConstant.PREFIX_CS + '/v1/boxType/getBoxTypeMap',
    },

    insuranceType : {
      list: CsConstant.PREFIX_CS + `/v1/insuranceType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/insuranceType`,
      delete: CsConstant.PREFIX_CS + `/v1/insuranceType`,
      update: CsConstant.PREFIX_CS + `/v1/insuranceType`,
      export: CsConstant.PREFIX_CS + `/v1/insuranceType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/insuranceType/getNextCode`,
      getBoxTypeMap: CsConstant.PREFIX_CS + '/v1/insuranceType/getBoxTypeMap',
    },
  },
  payment:{
    notifyHead : {
      list: CsConstant.PREFIX_CS + `/v1/notifyHead/list`,
      insert: CsConstant.PREFIX_CS + `/v1/notifyHead`,
      delete: CsConstant.PREFIX_CS + `/v1/notifyHead`,
      update: CsConstant.PREFIX_CS + `/v1/notifyHead`,
      export: CsConstant.PREFIX_CS + `/v1/notifyHead/export`,
      getDocNo: CsConstant.PREFIX_CS + `/v1/notifyHead/getDocNo`,
      getRate: CsConstant.PREFIX_CS + `/v1/notifyHead/getRate`,
      getUserInfo: CsConstant.PREFIX_CS + `/v1/notifyHead/getUserInfo`,
      confirm: CsConstant.PREFIX_CS + `/v1/notifyHead/confirm`,
      cancel: CsConstant.PREFIX_CS + `/v1/notifyHead/cancel`,
      back: CsConstant.PREFIX_CS + `/v1/notifyHead/back`,
      redFlush: CsConstant.PREFIX_CS + `/v1/notifyHead/redFlush`,
      copy: CsConstant.PREFIX_CS + `/v1/notifyHead/copy`,
      print: CsConstant.PREFIX_CS + `/v1/notifyHead/print`,

    },
    notifyList : {
      list: CsConstant.PREFIX_CS + `/v1/notifyList/list`,
      insert: CsConstant.PREFIX_CS + `/v1/notifyList`,
      insert1: CsConstant.PREFIX_CS + `/v1/notifyList/insert1`,
      insert6: CsConstant.PREFIX_CS + `/v1/notifyList/insert6`,
      insert3: CsConstant.PREFIX_CS + `/v1/notifyList/insert3`,
      insertOrder: CsConstant.PREFIX_CS + `/v1/notifyList/insertOrder`,
      insertOrder2: CsConstant.PREFIX_CS + `/v1/notifyList/insertOrder2`,
      insertOrder6: CsConstant.PREFIX_CS + `/v1/notifyList/insertOrder6`,
      insertOrder3: CsConstant.PREFIX_CS + `/v1/notifyList/insertOrder3`,
      delete: CsConstant.PREFIX_CS + `/v1/notifyList/delete`,
      update: CsConstant.PREFIX_CS + `/v1/notifyList`,
      export: CsConstant.PREFIX_CS + `/v1/notifyList/export`,

    },
    settlement : {
      list: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/list`,
      insert: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement`,
      delete: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement`,
      update: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement`,
      confirm: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/confirm`,
      back: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/back`,
      invalidate: CsConstant.PREFIX_CS + '/v1/bizPaymentSettlement/invalidate',
      export: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/export`
    },
    registrationHead: {
      list: CsConstant.PREFIX_CS + 'v1/registrationHead/list',
      planList: CsConstant.PREFIX_CS + 'v1/registrationHead/planList',
      customsList: CsConstant.PREFIX_CS + 'v1/registrationHead/customsList',
      update: CsConstant.PREFIX_CS + 'v1/registrationHead',
      insert: CsConstant.PREFIX_CS + 'v1/registrationHead',
      delete: CsConstant.PREFIX_CS + 'v1/registrationHead',
      export: CsConstant.PREFIX_CS + 'v1/registrationHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/registrationHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/registrationHead/sendAudit',
      cancel: CsConstant.PREFIX_CS + 'v1/registrationHead/cancel',
      back: CsConstant.PREFIX_CS + 'v1/registrationHead/back',
      redRush: CsConstant.PREFIX_CS + 'v1/registrationHead/redRush',
      checkStatus: CsConstant.PREFIX_CS + 'v1/registrationHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/registrationHead/copy',
      sellerList: CsConstant.PREFIX_CS + 'v1/registrationHead/sellerList',
      getDocNo: CsConstant.PREFIX_CS + 'v1/registrationHead/getDocNo',
    },
    registrationList: {
      list: CsConstant.PREFIX_CS + 'v1/registrationList/list',
      update: CsConstant.PREFIX_CS + 'v1/registrationList',
      insert: CsConstant.PREFIX_CS + 'v1/registrationList',
      delete: CsConstant.PREFIX_CS + 'v1/registrationList',
      export: CsConstant.PREFIX_CS + 'v1/registrationList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/registrationList/getContractTotal',
    },
    customerAccount:{
      list: CsConstant.PREFIX_CS + 'v1/customerAccount/list',
      update: CsConstant.PREFIX_CS + 'v1/customerAccount',
      insert: CsConstant.PREFIX_CS + 'v1/customerAccount',
      delete: CsConstant.PREFIX_CS + 'v1/customerAccount',
      export: CsConstant.PREFIX_CS + 'v1/customerAccount/export',
      confirm: CsConstant.PREFIX_CS + 'v1/customerAccount/confirm',
      redFlush: CsConstant.PREFIX_CS + 'v1/customerAccount/redFlush',
      back: CsConstant.PREFIX_CS + 'v1/customerAccount/back',
      checkIdNotCancel: CsConstant.PREFIX_CS + 'v1/customerAccount/checkIdNotCancel',
      invalidate: CsConstant.PREFIX_CS + 'v1/customerAccount/invalidate',
      copyVersion: CsConstant.PREFIX_CS + 'v1/customerAccount/copyVersion',
      print: CsConstant.PREFIX_CS + 'v1/customerAccount/print',
      contractListToCustomerAccount: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/listToCustomerAccount',
      shippingListToCustomerAccount: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/listToCustomerAccount',
      insertByShipping: CsConstant.PREFIX_CS + 'v1/customerAccount/insertByShipping',
      insertByContract: CsConstant.PREFIX_CS + 'v1/customerAccount/insertByContract',
    },
    bizCustomerAccountSummary:{
      list: CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/list`,
      insert:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary`,
      delete:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary`,
      update:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary`,
      export:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/export`,
      confirm:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/confirm`,
      invalidate:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/invalidate`,
      back:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/back`,
      getSupplierList:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/getSupplierList`,
      getCreateByList:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountSummary/getCreateUserList`,
      printSummary: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountSummary/printSummary',
      printContact: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountSummary/printContact',
      insertByContract: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountSummary/insertByContract',
      listCustomerAccountS: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountSummary/getForeignContractHeadList',
    },
    bizCustomerAccountTobacoo:{
      list: CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo/list`,
      insert:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo`,
      delete:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo`,
      update:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo`,
      export:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo/export`,
      confirm:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo/confirm`,
      getSupplierList:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo/getSupplierList`,
      getCreateByList:CsConstant.PREFIX_CS + `/v1/bizCustomerAccountTobacoo/getCreateUserList`,
      print: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountTobacoo/print',
      invalidate: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountTobacoo/invalidate',
      back: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountTobacoo/back',
      insertByContract: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountTobacoo/insertByContract',
      listCustomerAccountT: CsConstant.PREFIX_CS + 'v1/bizCustomerAccountTobacoo/getForeignContractHeadList',
    },
  },




  /* 进口订单表头信息 */
  bizIOrderHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/list',
    aeoList: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/aeoList',
    update: CsConstant.PREFIX_CS + 'v1/bizIOrderHead',
    insert: CsConstant.PREFIX_CS + 'v1/bizIOrderHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizIOrderHead',
    export: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/export',
    print: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/print',
    getIContractList: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/getIContractList',
    generateIOrder: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/generateIOrder',
    confirmIOrderHead: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/confirmIOrderHead',
    getOrderHeadTotal:CsConstant.PREFIX_CS + 'v1/bizIOrderHead/getOrderHeadTotal',
    copyVersion: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/copyVersion',
    checkOrderNoNotCancel: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/checkOrderNoNotCancel',
    checkNextModuleExistEffectiveData: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/checkNextModuleExistEffectiveData',
    cancelData: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/cancelData',
    getOrderSupplierList:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/getOrderSupplierList',
    getOrderCurrList:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/getOrderCurrList',
    rebootOrderNo:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/rebootOrderNo',
    checkOrderHeadIsNextModule:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/checkOrderHeadIsNextModule',
  },

  /*销售*/
  bizISellHead:{
    selectByheadId: CsConstant.PREFIX_CS + 'v1/BizISellHead/getSellHeadByOrderSid',
    update: CsConstant.PREFIX_CS + 'v1/BizISellHead',
    chargeback:CsConstant.PREFIX_CS + 'v1/BizISellHead/chargeback',
    chargebackIncoming:CsConstant.PREFIX_CS + 'v1/BizISellHead/chargebackIncoming',
    confirm:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirm',
    confirmIncoming:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirmIncoming',
    confirmReceipt:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirmReceipt',
    confirmRefreshList:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirmRefreshList',
  },
  bizISellList:{
    selectList: CsConstant.PREFIX_CS + 'v1/BizISellList/list',
    update: CsConstant.PREFIX_CS + 'v1/BizISellList',
    updateSellList: CsConstant.PREFIX_CS + 'v1/BizISellList/updateSellList',
    getSumDataByInvoice: CsConstant.PREFIX_CS + 'v1/BizISellList/getSumDataByInvoice',
    getSumDataByInvoiceSummary: CsConstant.PREFIX_CS + 'v1/BizISellList/getSumDataByInvoiceSummary',
  },
  /*入库*/
  bizIWarehouseReceiptHead:{
    selectByheadId: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead',
    abandonedData:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/abandonedData',
    generateBizIWarehouseReceipt:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/generateBizIWarehouseReceipt',
    checkPrintNotice:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/checkPrintNotice',

    printNotice:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/printNotice',
    onPrintOfLading:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/printOfLading',
    print: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/print',
    onSure: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/onSure',
  },
  bizIWarehouseReceiptList:{
    selectList: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList',
    getSumData: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/getSumData',
    extractTaxes: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/extractTaxes',
    getWarehouseReceiptListBySid: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/getWarehouseReceiptListBySid',
  },

  /*出库*/
  bizIReceiptHead:{
    selectByheadId: CsConstant.PREFIX_CS + 'v1/BizIReceiptHead/getReceiptHeadByOrderSid',
    update: CsConstant.PREFIX_CS + 'v1/BizIReceiptHead',
    export: CsConstant.PREFIX_CS + 'v1/BizIReceiptHead/export',
  },
  bizIReceiptList:{
    selectList: CsConstant.PREFIX_CS + 'v1/BizIReceiptList/list',
    update: CsConstant.PREFIX_CS + 'v1/BizIReceiptList',
    getSumDataByInvoiceSummary: CsConstant.PREFIX_CS + 'v1/BizIReceiptList/getSumDataByInvoiceSummary',
  },

  bizIOrderList: {
    list: CsConstant.PREFIX_CS + 'v1/bizIOrderList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIOrderList',
    insert: CsConstant.PREFIX_CS + 'v1/bizIOrderList',
    delete: CsConstant.PREFIX_CS + 'v1/bizIOrderList',
    export: CsConstant.PREFIX_CS + 'v1/bizIOrderList/export',
    getITotal: CsConstant.PREFIX_CS + 'v1/bizIOrderList/getITotal',
    getOrderListBySid: CsConstant.PREFIX_CS + 'v1/bizIOrderList/getOrderListBySid',
  },


  /* 进口进货信息  表头 */
  bizIPurchaseHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead',
    updateEntry: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/updateEntry',
    insert: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead',
    export: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/export',
    getPurchaseHeadByOrderSid: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/getPurchaseHeadByOrderSid',
  },
  /* 进口进货信息  表体 */
  bizIPurchaseList: {
    list: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList',
    insert: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList',
    delete: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList',
    export: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/export',
    innerUpdatePurchaseList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/innerUpdatePurchaseList',
    innerDecTotalUpdatePurchaseList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/innerDecTotalUpdatePurchaseList',
    innerUpdatePurchaseListInvoiceNo: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/innerUpdatePurchaseListInvoiceNo',
    getSumData: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/getSumData',
    getSumDataByInvoice: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/getSumDataByInvoice',
    addPushListInvoice: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/addPushListInvoice',
    deletePurchaseList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/deletePurchaseList',
    getPurchaseListBySid: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/getPurchaseListBySid',

  },

  /* 进口进货信息-装箱子表 */
  bizIPurchaseListBox: {
    list: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox',
    addPushListBox: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/addPushListBox',
    getSumData: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/getSumData',
    // 批量更新箱号
    batchUpdateBoxList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/batchUpdateBoxList',
  },









  /* >>>>>>>>>>>>>>>>>>>>>>> 第 3 条 线， 烟 机 设 备 ， 【进 货 单 模 块】 <<<<<<<<<<<<<<<<<<<<<<<< */
  /* 进口烟机设备 - 进货单 */
  bizSmokeMachineInComingHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/update',
    insert: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/insert',
    delete: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/delete',
    export: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/export',
    getCustomerList : CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/getCustomerList',
    getPortList : CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/getPortList',
    getCommonSearchList: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/getCommonSearchList',
    getIncomingGoodsHeadById: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/getIncomingGoodsHeadById',
    confirmIncomingGoodsHead: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/confirmIncomingGoodsHead',
    getExtractContractInfo: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/getExtractContractInfo',
    extractContract: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/extractContract',
    cancel: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/cancel',
    sendAudit: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/sendAudit',
    audit: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/audit',
    reject: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/reject',
    print: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/print',
    getSumTotal: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsHead/getSumTotal',
  },



  bizSmokeMachineInComingList: {
    list: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/update',
    insert: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/insert',
    delete: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/delete',
    getBizSmokeMachineIncomingGoodsListById: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/getBizSmokeMachineIncomingGoodsListById',
    updateQuantity: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/updateQuantity',
    updateUnitPrice: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/updateUnitPrice',
    updateCommonFiled: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/updateCommonFiled',
    getCommonKeyValueList: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsList/getCommonKeyValueList',
  },
  /* 证件信息 */
  bizSmokeMachineIncomingDocument: {
    getDocumentByHeadId: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsDocument/getDocumentByHeadId',
    updateAndInsert: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsDocument/updateAndInsert',
  },
  /* 投保信息 */
  bizSmokeMachineIncomingGoodsTb: {
    getTBByHeadId : CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsTb/getTBByHeadId',
    updateAndInsert : CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsTb/updateAndInsert',
    getCommonKeyValueList: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsTb/getCommonKeyValueList',
    calcPremium: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsTb/calcPremium',
    generateTB: CsConstant.PREFIX_CS + 'v1/bizSmokeMachineIncomingGoodsTb/generateTB'
  },






  /* >>>>>>>>>>>>>>>>>>>>>>> 第 7 条 线， 进 口 薄 片 ， 【分 析 单 模 块】 <<<<<<<<<<<<<<<<<<<<<<<< */


  bizBpAnalyseOrderHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/update',
    insert: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/insert',
    delete: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/delete',
    checkAnalyseOrderCode: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/checkAnalyseOrderCode',
    getCommonKeyValueList: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/getCommonKeyValueList',
    getCommonSearchList: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/getCommonSearchList',
    confirm: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/confirm',
    getContractNoList: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/getContractNoList',
    extractContract: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/extractContract',
    export: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/export',
    printAnalysis: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/printAnalysis',
    printShipment: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/printShipment',
    printShipping: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderHead/printShipping',
  },
  bizBpAnalyseOrderList: {
    list: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderList/list',
    getListById: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderList/getListById',
    update: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderList/update',
  },
  bizBpAnalyseOrderListBox:{
    list: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderListBox/list',
    delete: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderListBox/delete',
    getBoxCountByProductName: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderListBox/getBoxCountByProductName',
    getProductNameListByHeadId: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderListBox/getProductNameListByHeadId',
    insertContainerList: CsConstant.PREFIX_CS + 'v1/bizBpAnalyseOrderListBox/insertContainerList',
  },



  /* >>>>>>>>>>>>>>>>>>>>>>> 第 9 条 线， 非 国 营 贸 易 出 口 模 块 ， 【出 口 管 理】 <<<<<<<<<<<<<<<<<<<<<<<< */
  bizExportGoodsHead:{
    list: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/update',
    insert: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/insert',
    delete: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/delete',
    checkExportNo: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/checkExportNo',
    getCommonKeyValueList: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/getCommonKeyValueList',
    confirm: CsConstant.PREFIX_CS + 'v1/bizExportGoodsHead/confirm',
  },
  bizExportGoodsList:{
    list: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/list',
    getListById: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/getListById',
    update: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/update',
    updateNumOrPrice: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/updateNumOrPrice',
    updateStartBoxNo: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/updateStartBoxNo',
    updateEndBoxNo: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/updateEndBoxNo',
    updateGrossWeight: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/updateGrossWeight',
    updateNetWeight: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/updateNetWeight',
    updateGoodsLengthHeightWidth: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/updateGoodsLengthHeightWidth',
    getSummary: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/getSummary',
    delete: CsConstant.PREFIX_CS + 'v1/bizExportGoodsList/delete',
  },
  bizExportGoodsSellList:{
    list: CsConstant.PREFIX_CS + 'v1/bizExportGoodsSellList/list',
    getListTotal: CsConstant.PREFIX_CS + 'v1/bizExportGoodsSellList/getListTotal',
  },
  bizExportGoodsSellHead:{
    getFormDataById: CsConstant.PREFIX_CS + 'v1/bizExportGoodsSellHead/getFormDataById',
    confirm: CsConstant.PREFIX_CS + 'v1/bizExportGoodsSellHead/confirm',
    backOrder: CsConstant.PREFIX_CS + 'v1/bizExportGoodsSellHead/backOrder',
    redFlush: CsConstant.PREFIX_CS + 'v1/bizExportGoodsSellHead/redFlush',
  },




  /* >>>>>>>>>>>>>>>>>>>>>>> 第 2 条 线， 进口辅料 ， 【进 货 管 理 模 块】 <<<<<<<<<<<<<<<<<<<<<<<< */

  /* 进货管理 - 表头 */
  bizInComingHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/export',
    getSupplierList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getSupplierList',
    getPortList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getPortList',
    getCurrList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getCurrList',
    getPriceTermList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getPriceTermList',
    getUnitList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getUnitList',
    confirmIncomingGoods: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/confirmIncomingGoods',
    getExtractContractInfo: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getExtractContractInfo',
    extractContract: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/extractContract',
    getHeadInfoById: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getHeadInfoById',
    listNew: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/listNew',
    getSupplierListDistinct: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getSupplierListDistinct',
    cancel: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/cancel',
    returnOrder: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/returnOrder',
    redFlush: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/redFlush',
    generateTB: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/generateTB'
  },



  /* 进货管理 - 表体 */
  bizInComingList: {
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/update',
    updateInQuality: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/updateInQuality',
    updateQuantity: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/updateQuantity',
    updateAmount: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/updateAmount',
    updateInvoiceNo: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/updateInvoiceNo',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/insert',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/delete',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/export',
    getListSumByInvoice: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/getListSumByInvoice',
    batchUpdateInvoiceNo: CsConstant.PREFIX_CS + "v1/bizIncomingGoodsList/batchUpdateInvoiceNo",
    getIncomingGoodsListBySid: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/getIncomingGoodsListBySid',
    getSumTotalByHeadId: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/getSumTotalByHeadId',

  },

  /* 进货管理 - 证件信息 */
  bizIncomingGoodsDocument:{
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/export',
    getDocumentByHeadId: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/getDocumentByHeadId',
    insertOrUpdate: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/insertOrUpdate',
  },
  /* 进货管理- 投保信息 */
  bizIncomingGoodsInsurance:{
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/export',
    getDocumentByHeadId: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/getDocumentByHeadId',
    insertOrUpdate: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/insertOrUpdate',
  },

  /* 非国营辅料 进货管理 - 表头 */
  bizNonInComingHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead',
    insert: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead',
    export: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/export',
    getSupplierList: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getSupplierList',
    getPortList: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getPortList',
    getCurrList: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getCurrList',
    getPriceTermList: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getPriceTermList',
    getUnitList: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getUnitList',
    confirmIncomingGoods: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/confirmIncomingGoods',
    getExtractContractInfo: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getExtractContractInfo',
    extractContract: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/extractContract',
    getHeadInfoById: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getHeadInfoById',
    listNew: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/listNew',
    getSupplierListDistinct: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/getSupplierListDistinct',
    cancel: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/cancel',
    sendEntry: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsHead/sendEntry',
  },

  /* 非国营辅料 进货管理 - 表体 */
  bizNonInComingList: {
    list: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/update',
    updateInQuality: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/updateInQuality',
    updateQuantity: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/updateQuantity',
    updateAmount: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/updateAmount',
    updateInvoiceNo: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/updateInvoiceNo',
    insert: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/insert',
    batchInsert: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/batchInsert',
    delete: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/delete',
    export: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/export',
    getListSumByInvoice: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/getListSumByInvoice',
    batchUpdateInvoiceNo: CsConstant.PREFIX_CS + "v1/bizNonIncomingGoodsList/batchUpdateInvoiceNo",
    getIncomingGoodsListBySid: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/getIncomingGoodsListBySid',
    getSumTotalByHeadId: CsConstant.PREFIX_CS + 'v1/bizNonIncomingGoodsList/getSumTotalByHeadId',

  },

  /* 进口单证信息 */
  bizIDocument: {
    list: CsConstant.PREFIX_CS + 'v1/bizIOrderDocument/list',
    insert: CsConstant.PREFIX_CS + 'v1/bizIOrderDocument',
  },

  pcodeUrl: {
    list: '/api/pcode'
  },
  fuYun:{
    downloadTemplate:'/api/fyImportBackend/v1/importConfig/download'
  },

  quoUrl:{
    bizQuotation : {
      list: CsConstant.PREFIX_CS +`/v1/bizQuotation/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizQuotation`,
      delete: CsConstant.PREFIX_CS +`/v1/bizQuotation`,
      cancel: CsConstant.PREFIX_CS +`/v1/bizQuotation/cancel`,
      enable: CsConstant.PREFIX_CS +`/v1/bizQuotation/enable`,
      update: CsConstant.PREFIX_CS +`/v1/bizQuotation`,
      getGNameList: CsConstant.PREFIX_CS +`/v1/bizQuotation/getGNameList`,
      matForBuyContract: CsConstant.PREFIX_CS +`/v1/bizQuotation/matForBuyContract`,
      export: CsConstant.PREFIX_CS +`/v1/bizQuotation/export`,
      printCigarettePaper: CsConstant.PREFIX_CS +`/v1/bizQuotation/printCigarettePaper`,
      printTippingPaper: CsConstant.PREFIX_CS +`/v1/bizQuotation/printTippingPaper`,
    }
  },
  warehouseUrl:{
    bizStoreIHead:{
      list: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizStoreIHead`,
      delete: CsConstant.PREFIX_CS +`/v1/bizStoreIHead`,
      update: CsConstant.PREFIX_CS +`/v1/bizStoreIHead`,
      export: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/export`,
      cancel: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/invalidate`,
      confirm: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/confirm`,
      redFlush: CsConstant.PREFIX_CS + '/v1/bizStoreIHead/redFlush',
      checkIsNextModule: CsConstant.PREFIX_CS + '/v1/bizStoreIHead/checkIsNextModule',
      sendApproval: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/sendApproval`,
      generateIOrder: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/generateIOrder`,
      listIncomingGoods: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/listIncomingGoods`,
      printOutBoundReceipt: CsConstant.PREFIX_CS +`/v1/bizStoreEHead/printOutBoundReceipt`,
      printReceiptContainer: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/printReceiptContainer`,
      getOrderSupplierList: CsConstant.PREFIX_CS +`/v1/bizStoreIHead/getOrderSupplierList`,
    },
    bizStoreIList:{
      list: CsConstant.PREFIX_CS +`/v1/bizStoreIList/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizStoreIList`,
      delete: CsConstant.PREFIX_CS +`/v1/bizStoreIList`,
      update: CsConstant.PREFIX_CS +`/v1/bizStoreIList`,
      getListBySid: CsConstant.PREFIX_CS +`/v1/bizStoreIList/getListBySid`,
    },
    bizStoreEHead:{
      list: CsConstant.PREFIX_CS +`/v1/bizStoreEHead/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizStoreEHead`,
      delete: CsConstant.PREFIX_CS +`/v1/bizStoreEHead`,
      update: CsConstant.PREFIX_CS +`/v1/bizStoreEHead`,
      getListBySid: CsConstant.PREFIX_CS +`/v1/bizStoreEHead/getListBySid`,
      getStoreEHeadByHeadSid: CsConstant.PREFIX_CS +`/v1/bizStoreEHead/getStoreEHeadByHeadSid`,
      confirm: CsConstant.PREFIX_CS +`/v1/bizStoreEHead/confirm`,
      redFlush: CsConstant.PREFIX_CS +`/v1/bizStoreEHead/redFlush`,
    },
    bizStoreEList:{
      list: CsConstant.PREFIX_CS +`/v1/bizStoreEList/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizStoreEList`,
      delete: CsConstant.PREFIX_CS +`/v1/bizStoreEList`,
      update: CsConstant.PREFIX_CS +`/v1/bizStoreEList`,
      getListBySid: CsConstant.PREFIX_CS +`/v1/bizStoreEList/getListBySid`,
    },
  },
  nonAuxiliaryMaterials:{
    bizINonStateAuxmatAggrContractHead : {
      list: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractHead/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractHead`,
      delete: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractHead`,
      update: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractHead`,
      export: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractHead/export`,
      confirm: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/sendApproval',
      invalidate: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/invalidate',
      checkNotCancel: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/checkContractIdNotCancel',
      copyVersion: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/copyVersion',
      insertAggrContractWithDetails: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/insertAggrContractWithDetails',
      updateAggrContractWithDetails: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/updateAggrContractWithDetails',
      validateDetailData: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/validateDetailData',
      handlerTransferNotice: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/handlerTransferNotice',
      saveTransferNotice: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/saveTransferNotice',
      checkTransferNotice: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/checkTransferNotice',
      saveTransferNoticePrint: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/saveTransferNoticePrint',
      printAgreement: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/printAgreement',
      printContract: CsConstant.PREFIX_CS + 'v1/bizINonStateAuxmatAggrContractHead/printContract',
    },
    bizINonStateAuxmatAggrContractList : {
      list: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractList/list`,
      inList: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractList/inList`,
      insert: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractList`,
      delete: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractList`,
      update: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractList`,
      export: CsConstant.PREFIX_CS +`/v1/bizINonStateAuxmatAggrContractList/export`
    }
  },

  equipment: {
    bizIEquipmentAgentAgreement:{
      list: `${baseUri}/v1/bizIEquipmentAgentAgreement/list`,
      aeoList: `${baseUri}/v1/bizIEquipmentAgentAgreement/aeoList`,
      insert:`${baseUri}/v1/bizIEquipmentAgentAgreement`,
      delete:`${baseUri}/v1/bizIEquipmentAgentAgreement`,
      update:`${baseUri}/v1/bizIEquipmentAgentAgreement`,
      export:`${baseUri}/v1/bizIEquipmentAgentAgreement/export`,
      confirm:`${baseUri}/v1/bizIEquipmentAgentAgreement/confirm`,
      forContractList:`${baseUri}/v1/bizIEquipmentAgentAgreement/forContractList`,
      getDataByType:`${baseUri}/v1/bizIEquipmentAgentAgreement/getDataByType`, // 根据协议类型获取相关数据
    },
    bizIEquipmentAgentAgreementList:{
      list: `${baseUri}/v1/bizIEquipmentAgentAgreementList/list`,
      insert:`${baseUri}/v1/bizIEquipmentAgentAgreementList`,
      delete:`${baseUri}/v1/bizIEquipmentAgentAgreementList`,
      update:`${baseUri}/v1/bizIEquipmentAgentAgreementList`,
      export:`${baseUri}/v1/bizIEquipmentAgentAgreementList/export`,
      getSumTotal:`${baseUri}/v1/bizIEquipmentAgentAgreementList/getSumTotal`
    },
    bizIEquipmentPlanHead:{
      list: `${baseUri}/v1/bizIEquipmentPlanHead/list`,
      insert:`${baseUri}/v1/bizIEquipmentPlanHead`,
      delete:`${baseUri}/v1/bizIEquipmentPlanHead`,
      update:`${baseUri}/v1/bizIEquipmentPlanHead`,
      export:`${baseUri}/v1/bizIEquipmentPlanHead/export`,
      listForPlan:`${baseUri}/v1/equipment/foreignContract/head/listForPlan`,
      confirm: `${baseUri}/v1/bizIEquipmentPlanHead/confirm`,
      sendAudit: `${baseUri}/v1/bizIEquipmentPlanHead/sendAudit`,
      invalidate: `${baseUri}/v1/bizIEquipmentPlanHead/invalidate`,
      copyVersion: `${baseUri}/v1/bizIEquipmentPlanHead/copyVersion`,
      checkNotCancel: `${baseUri}/v1/bizIEquipmentPlanHead/checkNotCancel`,
    },
    bizIEquipmentPlanPayNotify:{
      list: `${baseUri}/v1/bizIEquipmentPlanPayNotify/list`,
      insert:`${baseUri}/v1/bizIEquipmentPlanPayNotify`,
      delete:`${baseUri}/v1/bizIEquipmentPlanPayNotify`,
      update:`${baseUri}/v1/bizIEquipmentPlanPayNotify`,
      export:`${baseUri}/v1/bizIEquipmentPlanPayNotify/export`,
      getTransMes: `${baseUri}/v1/bizIEquipmentPlanPayNotify/getTransMes`,
      getContainerList: `${baseUri}/v1/bizIEquipmentPlanPayNotify/getContainerList`,
      getContainerMes: `${baseUri}/v1/bizIEquipmentPlanPayNotify/getContainerMes`,
    },
    bizIEquipmentPlanList:{
      list: `${baseUri}/v1/bizIEquipmentPlanList/list`,
      insert:`${baseUri}/v1/bizIEquipmentPlanList`,
      delete:`${baseUri}/v1/bizIEquipmentPlanList`,
      update:`${baseUri}/v1/bizIEquipmentPlanList`,
      export:`${baseUri}/v1/bizIEquipmentPlanList/export`
    },
    bizIEquipmentContainerInfo:{
      list: `${baseUri}/v1/bizIEquipmentContainerInfo/list`,
      insert:`${baseUri}/v1/bizIEquipmentContainerInfo`,
      delete:`${baseUri}/v1/bizIEquipmentContainerInfo`,
      deleteByNotify:`${baseUri}/v1/bizIEquipmentContainerInfo/deleteByNotify`,
      update:`${baseUri}/v1/bizIEquipmentContainerInfo`,
      updateAll:`${baseUri}/v1/bizIEquipmentContainerInfo/updateAll`,
      export:`${baseUri}/v1/bizIEquipmentContainerInfo/export`
    },
    foreignContract: {
      head: {
        dollarRate: `${baseUri}/v1/equipment/foreignContract/head/dollarRate`,
        list: `${baseUri}/v1/equipment/foreignContract/head/list`,
        options: `${baseUri}/v1/equipment/foreignContract/head/options`,
        export: `${baseUri}/v1/equipment/foreignContract/head/export`,
        insert: `${baseUri}/v1/equipment/foreignContract/head`,
        update: `${baseUri}/v1/equipment/foreignContract/head`,
        delete: `${baseUri}/v1/equipment/foreignContract/head`,
        confirm: `${baseUri}/v1/equipment/foreignContract/head/confirm`,
        invalidate: `${baseUri}/v1/equipment/foreignContract/head/invalidate`,
        checkVersionCopy: `${baseUri}/v1/equipment/foreignContract/head/checkVersionCopy`,
        versionCopy: `${baseUri}/v1/equipment/foreignContract/head/versionCopy`
      },
      body: {
        getMaterial: `${baseUri}/v1/equipment/foreignContract/body/material`,
        list: `${baseUri}/v1/equipment/foreignContract/body/list`,
        summary: `${baseUri}/v1/equipment/foreignContract/body/summary`,
        insert: `${baseUri}/v1/equipment/foreignContract/body`,
        update: `${baseUri}/v1/equipment/foreignContract/body`,
        delete: `${baseUri}/v1/equipment/foreignContract/body`
      }
    }
  },
  deliveryOrder: {
    bizDeliveryOrderHead:{
      list: CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/list`,
      insert:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead`,
      delete:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead`,
      update:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead`,
      export:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/export`,
      printIns:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/printIns`,
      printLink:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/printLink`,
      confirm:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/confirm`,
      getCreateByList:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/getCreateUserList`,
      getCustomerList:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/getSupplierList`,
      getDeliveryOrderSid:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/getDeliveryOrderSid`,
      invalidate:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/invalidate`,
      back:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/back`,
      insertByContract:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/insertByContract`,
      listInDeliveryOrderHead:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderHead/listInDeliveryOrderHead`,
      sendEntry: CsConstant.PREFIX_CS + 'v1/bizDeliveryOrderHead/sendEntry',
    },
    bizDeliveryOrderList:{
      list: CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderList/list`,
      insert:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderList`,
      delete:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderList`,
      update:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderList`,
      export:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderList/export`,
      getDeliveryOrderListBySid:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderList/getDeliveryOrderListBySid`
    },
    bizDeliveryOrderContainerList:{
      list: CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderContainerList/list`,
      insert:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderContainerList`,
      delete:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderContainerList`,
      update:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderContainerList`,
      export:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderContainerList/export`,
      getDeliveryOrderCListBySid:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderContainerList/getDeliveryOrderCListBySid`,
    },
    bizDeliveryOrderShipments:{
      list: CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderShipments/list`,
      insert:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderShipments`,
      delete:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderShipments`,
      update:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderShipments`,
      export:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderShipments/export`,
      getDeliveryOrderShipmentsByHeadSid:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderShipments/getDeliveryOrderShipmentsByHeadSid`,
    },
    bizDeliveryOrderInsuranceInfo:{
      list: CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo/list`,
      insert:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo`,
      delete:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo`,
      update:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo`,
      export:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo/export`,
      getDeliveryOrderInsuranceInfoByHeadSid:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo/getDeliveryOrderInsuranceInfoByHeadSid`,
      getExchangeRate:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderInsuranceInfo/getExchangeRate`
    },
    bizDeliveryOrderCert:{
      list: CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderCert/list`,
      insert:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderCert`,
      delete:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderCert`,
      update:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderCert`,
      export:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderCert/export`,
      getDeliveryOrderCertByHeadSid:CsConstant.PREFIX_CS +`/v1/bizDeliveryOrderCert/getDeliveryOrderCertByHeadSid`,
    }
  },
  purchaseOrder: {
    bizPurchaseOrderHead:{
      list: `${baseUri}/v1/bizPurchaseOrderHead/list`,
      insert:`${baseUri}/v1/bizPurchaseOrderHead`,
      delete:`${baseUri}/v1/bizPurchaseOrderHead`,
      update:`${baseUri}/v1/bizPurchaseOrderHead`,
      export:`${baseUri}/v1/bizPurchaseOrderHead/export`,
      sendEntry:`${baseUri}/v1/bizPurchaseOrderHead/sendEntry`,
      insertByContract:`${baseUri}/v1/bizPurchaseOrderHead/insertByContract`,
      insertListByContract:`${baseUri}/v1/bizPurchaseOrderHead/insertListByContract`,
      listInPurchaseOrderHead:`${baseUri}/v1/bizPurchaseOrderHead/listInPurchaseOrderHead`,
      confirm:CsConstant.PREFIX_CS +`/v1/bizPurchaseOrderHead/confirm`,
      invalidate: CsConstant.PREFIX_CS + 'v1/bizPurchaseOrderHead/invalidate',
      getCreateByList:CsConstant.PREFIX_CS + `/v1/bizPurchaseOrderHead/getCreateUserList`,
      getPurchaseOrderSid:CsConstant.PREFIX_CS +`/v1/bizPurchaseOrderHead/getPurchaseOrderSid`,
      print:CsConstant.PREFIX_CS +`/v1/bizPurchaseOrderHead/print`,
    },
    bizPurchaseOrderCert:{
      list: `${baseUri}/v1/bizPurchaseOrderCert/list`,
      insert:`${baseUri}/v1/bizPurchaseOrderCert`,
      delete:`${baseUri}/v1/bizPurchaseOrderCert`,
      update:`${baseUri}/v1/bizPurchaseOrderCert`,
      export:`${baseUri}/v1/bizPurchaseOrderCert/export`,
      getPurchaseOrderCertByHeadSid:`${baseUri}/v1/bizPurchaseOrderCert/getPurchaseOrderCertByHeadSid`
    },
    bizPurchaseOrderList:{
      list: `${baseUri}/v1/bizPurchaseOrderList/list`,
      insert:`${baseUri}/v1/bizPurchaseOrderList`,
      delete:`${baseUri}/v1/bizPurchaseOrderList`,
      update:`${baseUri}/v1/bizPurchaseOrderList`,
      export:`${baseUri}/v1/bizPurchaseOrderList/export`
    }
  },
  iEBusiness: {
    bizAgencyAgreement:{
      list: `${baseUri}/v1/bizAgencyAgreement/list`,
      forContractList: `${baseUri}/v1/bizAgencyAgreement/forContractList`,
      aeoList: `${baseUri}/v1/bizAgencyAgreement/aeoList`,
      insert:`${baseUri}/v1/bizAgencyAgreement`,
      delete:`${baseUri}/v1/bizAgencyAgreement`,
      update:`${baseUri}/v1/bizAgencyAgreement`,
      export:`${baseUri}/v1/bizAgencyAgreement/export`,
      confirm:`${baseUri}/v1/bizAgencyAgreement/confirm`,
      print:`${baseUri}/v1/bizAgencyAgreement/print`,
      sign:`${baseUri}/v1/bizAgencyAgreement/sign`,
    },
    bizAgencyAgreementList:{
      list: `${baseUri}/v1/bizAgencyAgreementList/list`,
      insert:`${baseUri}/v1/bizAgencyAgreementList`,
      delete:`${baseUri}/v1/bizAgencyAgreementList`,
      update:`${baseUri}/v1/bizAgencyAgreementList`,
      export:`${baseUri}/v1/bizAgencyAgreementList/export`,
      getSumTotal:`${baseUri}/v1/bizAgencyAgreementList/getSumTotal`,
    }
  },
  seven: {
    foreignContract: {
      head: {
        list: `${baseUri}/v1/seven/foreignContract/head/list`,
        options: `${baseUri}/v1/seven/foreignContract/head/options`,
        export: `${baseUri}/v1/seven/foreignContract/head/export`,
        insert: `${baseUri}/v1/seven/foreignContract/head`,
        update: `${baseUri}/v1/seven/foreignContract/head`,
        delete: `${baseUri}/v1/seven/foreignContract/head`,
        confirm: `${baseUri}/v1/seven/foreignContract/head/confirm`,
        invalidate: `${baseUri}/v1/seven/foreignContract/head/invalidate`,
        checkVersionCopy: `${baseUri}/v1/seven/foreignContract/head/checkVersionCopy`,
        versionCopy: `${baseUri}/v1/seven/foreignContract/head/versionCopy`,
        printSheet: `${baseUri}/v1/seven/foreignContract/head/print`
      },
      body: {
        getMaterial: `${baseUri}/v1/seven/foreignContract/body/material`,
        list: `${baseUri}/v1/seven/foreignContract/body/list`,
        summary: `${baseUri}/v1/seven/foreignContract/body/summary`,
        insert: `${baseUri}/v1/seven/foreignContract/body`,
        update: `${baseUri}/v1/seven/foreignContract/body`,
        delete: `${baseUri}/v1/seven/foreignContract/body`
      }
    }
  }
}
