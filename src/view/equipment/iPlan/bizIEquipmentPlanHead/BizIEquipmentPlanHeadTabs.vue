<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="表头" >
          <BizIEquipmentPlanHeadEdit  ref="headTab"  :edit-config="editConfig"  @onEditBack="editBack"></BizIEquipmentPlanHeadEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="license" tab="证件信息" @onEditBack="editBack" >
          <BizIEquipmentPlanHeadLicense :edit-config="editConfig" @onEditBack="editBack"></BizIEquipmentPlanHeadLicense>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="payNotify" tab="划款通知" @onEditBack="editBack" >
          <BizIEquipmentPlanHeadPayNotify ref="payNotify" :edit-config="editConfig" @onEditBack="editBack"></BizIEquipmentPlanHeadPayNotify>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="attach" tab="归档附件" @onEditBack="editBack" >
          <biz-i-attach :head-id="headId"  :operation-status="editConfig.editStatus"></biz-i-attach>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="aeo" tab="审批记录" @onEditBack="editBack" >
          <!-- 确保子组件重新挂载 -->
          <cs-aeo-info-list :sid="headId"></cs-aeo-info-list>
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>
  </section>
</template>
<script setup>
import {onMounted, reactive, ref, watch, getCurrentInstance} from "vue";
import {editStatus} from "@/view/common/constant";
import BizIEquipmentPlanHeadEdit from "./BizIEquipmentPlanHeadEdit.vue";
import BizIEquipmentPlanHeadLicense from "./BizIEquipmentPlanHeadLicense.vue";
import BizIEquipmentPlanHeadPayNotify from "./BizIEquipmentPlanHeadPayNotify.vue";
import BizIAttach from "@/view/audit/importedCigarettes/contract/components/BizIAttach.vue";
import CsAeoInfoList from '@/components/aeo/CsAeoInfoList.vue'
defineOptions({
  name:'BizIEquipmentPlanHeadTabs'
})
const emit = defineEmits(['onEditBack'])
/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}
/* 激活Tab key */
const tabName = ref('headTab');
/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  license:true,
  payNotify:true,
  attach:true,
  aeo:true,
})
/* 表头headId */
const headId = ref('')
/* 是否显示子模块 tab */
const showBody = ref(false)
/* 划款通知组件引用 */
const payNotify = ref(null)
/* 返回tab界面 */
const editBack = (val) => {
  // 检查是否是来自tab返回的特殊标记，避免循环
  if (val && typeof val === 'object' && val.fromTabBack) {
    emit('onEditBack', true)
    return;
  }

  // 安全地检查 val 是否是对象且有 editStatus 属性
  if (val && typeof val === 'object' && val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    headId.value = val.editData.id
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
  }else {
    // 如果是点击tab页面上的返回icon（val为undefined或true）且当前在划款通知tab页面
    if ((!val || val === true) && tabName.value === 'payNotify' && payNotify.value) {
      // 调用划款通知组件的handleTabBack方法
      if (typeof payNotify.value.handleTabBack === 'function') {
        payNotify.value.handleTabBack();
        return;
      }
    }

    // 其他情况直接返回
    emit('onEditBack', val || true)
  }
}
/* 初始化操作 */
onMounted(()=>{
  console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = props.editConfig.editData.id
    showBody.value = true;
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.id
    console.log('headId.value', headId.value)
    showBody.value = true;
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    headId.value = props.editConfig.editData.id
    showBody.value = true
  }
})
/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
})
</script>
<style lang="less" scoped>
</style>
