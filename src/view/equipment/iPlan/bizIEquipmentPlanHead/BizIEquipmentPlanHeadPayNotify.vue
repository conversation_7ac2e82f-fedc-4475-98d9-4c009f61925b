<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            新增
          </a-button>
        </div>
        <div class="cs-action-btn-item">
          <a-button size="small" @click="handlerEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange"/>
            </template>
            编辑
          </a-button>
        </div>
        <div class="cs-action-btn-item">
          <a-button size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            删除
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
              :resId="tableKey"
              :tableKey="tableKey+'-paynotify'"
              :initSettingColumns="originalColumns"
              :showColumnSettings="true"
              @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight, x: 400 }"
          column-drag
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ? showColumns : totalColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :custom-row="customRow"
          :row-height="30"
          :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                  style="color:#e93f41; padding: 0 4px;"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                  style="color:#1677ff; padding: 0 4px;"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>
            <template v-else-if="column.key === 'paymentType'">
              {{ formatPaymentType(record.paymentType) }}
            </template>
            <template v-else-if="column.key === 'contractAmount' || column.key === 'exchangeRate' || column.key === 'importExportAgentFee' || column.key === 'headOfficeAgentFee' || column.key === 'freightForwardingFee' || column.key === 'insuranceFee' || column.key === 'remittanceAmountRmb'">
              {{ formatNumber(record[column.key]) }}
            </template>
            <template v-else-if="column.key === 'importExportAgentRate' || column.key === 'headOfficeAgentRate' || column.key === 'insuranceRate'">
              {{ formatPercent(record[column.key]) }}
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class="cs-pagination" v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination
          size="small"
          v-model:current="page.current"
          show-size-changer
          :page-size="page.pageSize"
          :total="page.total"
          @change="onPageChange"
        >
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- 新增/编辑表单 -->
    <div v-if="!show">
      <BizIEquipmentPlanHeadPayNotifyEdit
        ref="editRef"
        @onEditBack="handlerOnBack"
        :editConfig="editConfig"
      />
    </div>
  </section>
</template>

<script setup>
import { onMounted, reactive, ref, computed, watch } from "vue";
import { editStatus } from "@/view/common/constant";
import { message, Modal } from "ant-design-vue";
import {
  getEquipmentPlanPayNotifyList,
  deleteEquipmentPlanPayNotify
} from "@/api/equipment/equipmentPlanApi";
import BizIEquipmentPlanHeadPayNotifyEdit from "./BizIEquipmentPlanHeadPayNotifyEdit.vue";
import { getColumns } from './bizIEquipmentPlanHeadPayNotifyColumns';
import { deepClone } from "@/view/utils/common";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import { useRoute } from "vue-router";

defineOptions({
  name: 'BizIEquipmentPlanHeadPayNotify'
})

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => ({})
  }
});

// 定义子组件 emit事件
const emit = defineEmits(['onEditBack']);

// 获取列定义
const { totalColumns } = getColumns();

// 页面状态
const show = ref(true);
const tableHeight = ref(400);
const tableLoading = ref(false);
const deleteLoading = ref(false);

// 分页配置
const page = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表格选择
const gridData = reactive({
  selectedRowKeys: []
});

// 编辑配置
const editConfig = ref({});

// 表格引用
const tableRef = ref();

// 列管理相关
const showColumns = ref([]);
const originalColumns = ref();
const tableKey = ref('');

// 设置表格唯一键
tableKey.value = useRoute().path;

// 表格选择变化
const onSelectChange = (selectedRowKeys) => {
  gridData.selectedRowKeys = selectedRowKeys;
};

// 自定义行属性
const customRow = (record) => {
  return {
    onClick: () => {
      // 单击选中行
      const index = gridData.selectedRowKeys.indexOf(record.id);
      if (index > -1) {
        gridData.selectedRowKeys.splice(index, 1);
      } else {
        gridData.selectedRowKeys = [record.id];
      }
    }
  };
};

// 数据源
const dataSourceList = ref([]);

// 格式化款项类型显示
const formatPaymentType = (paymentType) => {
  if (!paymentType) return '';
  return paymentType.split(',').join('、');
};

// 格式化数字显示
const formatNumber = (value) => {
  if (!value && value !== 0) return '';
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 格式化百分比显示
const formatPercent = (value) => {
  if (!value && value !== 0) return '';
  return `${value}%`;
};

// 操作按钮样式控制
const operationEdit = (type) => {
  // 这里可以根据权限或状态控制按钮的显示
  return {};
};

// 加载数据
const loadData = async () => {
  console.log('loadData called, props.editConfig:', props.editConfig);
  console.log('editData:', props.editConfig?.editData);
  console.log('editData.id:', props.editConfig?.editData?.id);

  if (!props.editConfig?.editData?.id) {
    console.log('没有headId，无法加载数据');
    return;
  }

  try {
    tableLoading.value = true;
    const params = {
      headId: props.editConfig.editData.id,
      pageNo: page.current,
      pageSize: page.pageSize
    };

    console.log('请求参数:', params);

    const result = await getEquipmentPlanPayNotifyList(params);
    console.log('API返回结果:', result);

    if (result.code === 200) {
      // 根据实际返回的数据结构调整
      dataSourceList.value = result.data || [];
      page.total = result.total || 0;
      console.log('设置数据源:', dataSourceList.value);
      console.log('总数:', page.total);
    } else {
      console.error('API返回错误:', result.message);
      message.error(result.message || '加载数据失败');
    }
  } catch (error) {
    console.error('加载数据失败', error);
    message.error('加载数据失败');
  } finally {
    tableLoading.value = false;
  }
};

// 新增
const handlerAdd = () => {
  if (props.editConfig.editData.status!=='1'){
    message.error('请先确认表头表体数据');
    return;
  }

  // 生成唯一性ID用于新增记录

  editConfig.value = {
    editStatus: editStatus.ADD,
    editData: {

      headId: props.editConfig.editData.id,
      businessType: props.editConfig.editData.businessType
    }
  };
  show.value = false;
};

// 编辑
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length === 0) {
    message.warning('请选择要编辑的数据');
    return;
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据进行编辑');
    return;
  }

  const selectedRecord = dataSourceList.value.find(item => item.id === gridData.selectedRowKeys[0]);
  editConfig.value = {
    editStatus: editStatus.EDIT,
    editData: selectedRecord
  };
  show.value = false;
};

// 行内编辑
const handleEditByRow = (record) => {
  editConfig.value = {
    editStatus: editStatus.EDIT,
    editData: record
  };
  show.value = false;
};

// 行内查看
const handleViewByRow = (record) => {
  editConfig.value = {
    editStatus: editStatus.SHOW,
    editData: record
  };
  show.value = false;
};

// 删除
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${gridData.selectedRowKeys.length} 条记录吗？`,
    onOk: async () => {
      try {
        deleteLoading.value = true;
        const result = await deleteEquipmentPlanPayNotify(gridData.selectedRowKeys.join(','));
        if (result.code === 200) {
          message.success('删除成功');
          gridData.selectedRowKeys = [];
          await loadData();
        } else {
          message.error(result.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败', error);
        message.error('删除失败');
      } finally {
        deleteLoading.value = false;
      }
    }
  });
};

// 返回处理
const handlerOnBack = (val) => {
  show.value = true;
  if (val === true) {
    // 刷新数据
    loadData();
  }
};

// 页面变化处理
const onPageChange = (current, size) => {
  page.current = current;
  page.pageSize = size;
  loadData();
};

// 自定义显示列初始化操作
const initCustomColumn = () => {
  // 这里是拷贝属性
  let tempColumns = deepClone(totalColumns.value);
  let dealColumns = [];
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
};

// 选中visible为true的数据进行显示
const customColumnChange = (settingColumns) => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value];
};

// 监控 totalColumns 变化
watch(totalColumns, (newValue, oldValue) => {
  if (newValue.length === 0) {
    showColumns.value = [...totalColumns.value];
  } else {
    showColumns.value = newValue.map((item) => {
      item.visible = true;
      return item;
    });
  }
}, { deep: true });

// 组件挂载时加载数据
onMounted(() => {
  console.log('组件挂载，开始加载数据');
  initCustomColumn();
  // 初始化显示列
  showColumns.value = [...totalColumns.value];
  loadData();
});
</script>

<style lang="less" scoped>
.operation-container {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.cs-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.count-number {
  color: #666;
  font-size: 14px;
}
</style>
