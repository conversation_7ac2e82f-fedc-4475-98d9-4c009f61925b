<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="表头表体" >
          <NotifyHeadEdit  ref="headTab"  :edit-config="editConfig"  @onEditBack="editBack" @headId="getHeadId"></NotifyHeadEdit>
        </a-tab-pane>
        <a-tab-pane v-if="editConfig.editStatus !== editStatus.ADD" key="attach" tab="归档附件" @onEditBack="editBack" >
          <NotifyAttach :head-id="headId"  :operation-status="editConfig.editStatus" ></NotifyAttach>
        </a-tab-pane>
        <template #rightExtra>
            <div class="cs-tab-icon" @click="editBack">
              <GlobalIcon type="close-circle" style="color:#000"/>
            </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch, createVNode} from "vue";
import {editStatus} from "@/view/common/constant";
import NotifyHeadEdit from "./NotifyHeadEdit.vue";
import NotifyAttach from "@/view/payment/notify/NotifyAttach.vue";
import {Modal} from "ant-design-vue";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";

defineOptions({
  name:'NotifyTab'
})

const emit = defineEmits(['onEditBack'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(false)



/* 返回tab界面 */
const editBack = (val) => {
  if (val && val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    headId.value = val.editData.sid
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
  } else {
    // 检查是否是新增状态且未保存过
    if (props.editConfig.editStatus === editStatus.ADD && !props.editConfig.hasSaved) {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        cancelText: '取消',
        content: '您尚未保存数据，是否确定返回？返回后当前已维护的表头数据无效。',
        onOk: () => {
          // 确定返回，传递返回标记
          if (val) {
            emit('onEditBack', val)
          } else {
            emit('onEditBack', true)
          }
        },
        onCancel: () => {
          // 取消返回，不做任何操作
        }
      });
    } else {
      // 已保存或非新增状态，直接返回
      if (val) {
        emit('onEditBack', val)
      } else {
        emit('onEditBack', true)
      }
    }
  }
}

const getHeadId = (data) => {
  headId.value = data
};


/* 初始化操作 */
onMounted(()=>{
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    // headId.value = props.editConfig.editData.sid
    showBody.value = false;
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.sid
    showBody.value = true;
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    headId.value = props.editConfig.editData.sid
    showBody.value = true
  }
})



/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
})

</script>

<style lang="less" scoped>

</style>
